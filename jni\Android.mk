LOCAL_PATH := $(call my-dir)

include $(CLEAR_VARS)

LOCAL_MODULE := bs.sh

# 通用编译标志
COMMON_FLAGS += -O2 -ffunction-sections -fdata-sections -fvisibility=hidden -fvisibility-inlines-hidden
COMMON_FLAGS += -fexceptions -frtti

LOCAL_CFLAGS := -std=c++17 $(COMMON_FLAGS)
LOCAL_CPPFLAGS := -std=c++17 $(COMMON_FLAGS)
LOCAL_CFLAGS += -Wno-error=format-security -w -fpermissive
LOCAL_CPPFLAGS += -Wno-error=format-security -fpermissive -w -fms-extensions -Wno-error=c++11-narrowing

# 添加头文件搜索路径
LOCAL_C_INCLUDES := $(LOCAL_PATH)/include

# 添加源文件
LOCAL_SRC_FILES := src/main.cpp \
                   src/GyroHook.cpp \


# 链接库和系统库（移除-lz因为我们使用静态库）
LOCAL_LDLIBS += -llog -landroid -lEGL -lGLESv1_CM -lGLESv2 -lGLESv3

# 构建可执行文件
include $(BUILD_EXECUTABLE)

