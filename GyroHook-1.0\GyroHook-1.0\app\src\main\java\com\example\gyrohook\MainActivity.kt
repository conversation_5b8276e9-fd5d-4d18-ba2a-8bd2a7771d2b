package com.example.gyrohook

// Android系统相关导入
import android.os.Bundle
import android.widget.Button
import android.widget.EditText
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import android.content.Context
import android.util.Log

// 网络相关导入
import java.net.ServerSocket
import java.net.Socket
import java.net.BindException
import java.net.SocketException
import java.io.BufferedReader
import java.io.InputStreamReader

// 协程相关导入
import kotlinx.coroutines.*

// UI组件导入
import com.google.android.material.switchmaterial.SwitchMaterial

// 文件操作相关导入
import java.io.File
import org.json.JSONObject
import java.io.FileWriter

// 视图绑定导入
import com.example.gyrohook.databinding.ActivityMainBinding

/**
 * 主活动类 - GyroHook应用的核心界面
 * 功能：
 * 1. 管理陀螺仪旋转数据的显示和编辑
 * 2. 提供Socket服务器功能，接收外部陀螺仪数据
 * 3. 保存和加载用户设置
 */
class MainActivity : AppCompatActivity() {
    // 视图绑定对象，用于访问布局中的UI组件
    private lateinit var binding: ActivityMainBinding

    // UI组件引用
    private lateinit var etRotationX: EditText    // X轴旋转输入框
    private lateinit var etRotationY: EditText    // Y轴旋转输入框
    private lateinit var etRotationZ: EditText    // Z轴旋转输入框
    private lateinit var etPort: EditText         // 端口号输入框
    private lateinit var btnApply: Button         // 应用设置按钮
    private lateinit var switchSocket: SwitchMaterial  // Socket服务器开关

    // Socket服务器相关变量
    private var serverSocket: ServerSocket? = null    // 服务器Socket实例
    private var serverJob: Job? = null               // 服务器协程任务
    private val coroutineScope = CoroutineScope(Dispatchers.IO + Job())  // 协程作用域
    private var isServerRunning = false             // 服务器运行状态标志
    private val PREF_NAME = "gyro_settings"         // SharedPreferences文件名

    companion object {
        // 日志标签
        private const val TAG = "GyroHook"

        // SharedPreferences键名常量
        private const val PREF_ROTATION_X = "rotation_x"  // X轴旋转键名
        private const val PREF_ROTATION_Y = "rotation_y"  // Y轴旋转键名
        private const val PREF_ROTATION_Z = "rotation_z"  // Z轴旋转键名
        private const val PREF_PORT = "socket_port"       // 端口号键名
        private const val DEFAULT_PORT = 16384            // 默认端口号
    }

    /**
     * Activity创建时的回调方法
     * 初始化UI组件、加载设置、设置事件监听器
     */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // 初始化视图绑定
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 尝试加载之前保存的设置（兼容旧版本）
        try {
            val prefs = getSharedPreferences(PREF_NAME, Context.MODE_WORLD_READABLE)
            binding.etRotationX.setText(prefs.getFloat("x", 0f).toString())
            binding.etRotationY.setText(prefs.getFloat("y", 0f).toString())
            binding.etRotationZ.setText(prefs.getFloat("z", 0f).toString())
        } catch (e: Exception) {
            Log.e("GyroHook", "Failed to load preferences", e)
            Toast.makeText(this, "Failed to load settings", Toast.LENGTH_SHORT).show()
        }

        // 设置应用按钮的点击事件（兼容旧版本）
        binding.btnApply.setOnClickListener {
            try {
                val prefs = getSharedPreferences(PREF_NAME, Context.MODE_WORLD_READABLE)
                prefs.edit().apply {
                    putFloat("x", binding.etRotationX.text.toString().toFloatOrNull() ?: 0f)
                    putFloat("y", binding.etRotationY.text.toString().toFloatOrNull() ?: 0f)
                    putFloat("z", binding.etRotationZ.text.toString().toFloatOrNull() ?: 0f)
                    apply()
                }
                Toast.makeText(this, "Settings saved", Toast.LENGTH_SHORT).show()
            } catch (e: Exception) {
                Log.e("GyroHook", "Failed to save preferences", e)
                Toast.makeText(this, "Failed to save settings", Toast.LENGTH_SHORT).show()
            }
        }

        // 初始化各个组件
        initializeViews()        // 初始化UI组件引用
        loadSettings()          // 加载保存的设置
        setupApplyButton()      // 设置应用按钮事件
        setupSocketSwitch()     // 设置Socket开关事件

        // 确保文件权限正确设置
        ensureFilePermissions()
    }

    /**
     * 确保设置文件的权限正确设置
     * 创建默认设置文件（如果不存在）并设置为可读
     */
    private fun ensureFilePermissions() {
        try {
            // 创建设置文件路径
            val settingsFile = File(dataDir, PREF_NAME)

            // 如果设置文件不存在，创建默认设置
            if (!settingsFile.exists()) {
                val defaultSettings = JSONObject().apply {
                    put(PREF_ROTATION_X, 0f)        // 默认X轴旋转为0
                    put(PREF_ROTATION_Y, 0f)        // 默认Y轴旋转为0
                    put(PREF_ROTATION_Z, 0f)        // 默认Z轴旋转为0
                    put(PREF_PORT, DEFAULT_PORT)    // 默认端口号
                }
                // 写入默认设置到文件
                FileWriter(settingsFile).use { it.write(defaultSettings.toString()) }
            }

            // 设置文件为全局可读
            settingsFile.setReadable(true, false)
            Log.d(TAG, "Settings file path: ${settingsFile.absolutePath}")
            Log.d(TAG, "Settings file readable: ${settingsFile.canRead()}")
        } catch (e: Exception) {
            Log.e(TAG, "Error ensuring file permissions: ${e.message}")
        }
    }

    /**
     * 初始化UI组件引用
     * 从视图绑定对象中获取各个UI组件的引用
     */
    private fun initializeViews() {
        try {
            etRotationX = binding.etRotationX    // X轴旋转输入框
            etRotationY = binding.etRotationY    // Y轴旋转输入框
            etRotationZ = binding.etRotationZ    // Z轴旋转输入框
            etPort = binding.etPort              // 端口号输入框
            btnApply = binding.btnApply          // 应用设置按钮
            switchSocket = binding.switchSocket  // Socket服务器开关
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing views: ${e.message}")
            throw e  // 重新抛出异常，因为UI初始化失败是致命错误
        }
    }

    /**
     * 从SharedPreferences加载保存的设置
     * 如果加载失败，则使用默认值
     */
    private fun loadSettings() {
        try {
            // 获取SharedPreferences实例
            val prefs = getSharedPreferences(PREF_NAME, Context.MODE_WORLD_READABLE)

            // 加载各个设置值并设置到对应的输入框
            etRotationX.setText(prefs.getFloat("x", 0f).toString())                    // 加载X轴旋转值
            etRotationY.setText(prefs.getFloat("y", 0f).toString())                    // 加载Y轴旋转值
            etRotationZ.setText(prefs.getFloat("z", 0f).toString())                    // 加载Z轴旋转值
            etPort.setText(prefs.getInt("socket_port", DEFAULT_PORT).toString())       // 加载端口号

            Log.d(TAG, "Settings loaded successfully")
        } catch (e: Exception) {
            // 加载失败时使用默认值
            Log.e(TAG, "Error loading settings: ${e.message}")
            etRotationX.setText("0")                        // 默认X轴旋转为0
            etRotationY.setText("0")                        // 默认Y轴旋转为0
            etRotationZ.setText("0")                        // 默认Z轴旋转为0
            etPort.setText(DEFAULT_PORT.toString())         // 默认端口号
        }
    }

    /**
     * 保存设置到SharedPreferences
     * @param x X轴旋转值
     * @param y Y轴旋转值
     * @param z Z轴旋转值
     * @param port Socket服务器端口号，默认为DEFAULT_PORT
     */
    private fun saveSettings(x: Float, y: Float, z: Float, port: Int = DEFAULT_PORT) {
        try {
            // 获取SharedPreferences实例并保存设置
            val prefs = getSharedPreferences(PREF_NAME, Context.MODE_WORLD_READABLE)
            prefs.edit().apply {
                putFloat("x", x)                    // 保存X轴旋转值
                putFloat("y", y)                    // 保存Y轴旋转值
                putFloat("z", z)                    // 保存Z轴旋转值
                putInt("socket_port", port)         // 保存端口号
                apply()                             // 应用更改
            }

            // 设置SharedPreferences文件的权限，使其可被外部访问
            val dataDir = File(applicationInfo.dataDir)
            val sharedPrefsDir = File(dataDir, "shared_prefs")
            val prefsFile = File(sharedPrefsDir, "${PREF_NAME}.xml")

            if (prefsFile.exists()) {
                prefsFile.setReadable(true, false)          // 设置文件为全局可读
                sharedPrefsDir.setExecutable(true, false)   // 设置目录为全局可执行
                sharedPrefsDir.setReadable(true, false)     // 设置目录为全局可读
                Log.d(TAG, "Set permissions for: ${prefsFile.absolutePath}")
            }

            Log.d(TAG, "Settings saved - X: $x, Y: $y, Z: $z, Port: $port")
        } catch (e: Exception) {
            Log.e(TAG, "Error saving settings: ${e.message}")
            Toast.makeText(this, "保存失败：${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 设置应用按钮的点击事件监听器
     * 当用户点击应用按钮时，保存当前输入的设置值
     */
    private fun setupApplyButton() {
        btnApply.setOnClickListener {
            try {
                // 从输入框获取值，如果解析失败则使用默认值
                val x = etRotationX.text.toString().toFloatOrNull() ?: 0f      // 获取X轴旋转值
                val y = etRotationY.text.toString().toFloatOrNull() ?: 0f      // 获取Y轴旋转值
                val z = etRotationZ.text.toString().toFloatOrNull() ?: 0f      // 获取Z轴旋转值
                val port = etPort.text.toString().toIntOrNull() ?: DEFAULT_PORT // 获取端口号

                // 保存设置并显示成功消息
                saveSettings(x, y, z, port)
                Toast.makeText(this, "设置已保存！", Toast.LENGTH_SHORT).show()
            } catch (e: Exception) {
                // 保存失败时记录错误并显示错误消息
                Log.e(TAG, "Error saving settings: ${e.message}")
                Toast.makeText(this, "保存失败：${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    /**
     * 设置Socket服务器开关的状态变化监听器
     * 根据开关状态启动或停止Socket服务器
     */
    private fun setupSocketSwitch() {
        switchSocket.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked && !isServerRunning) {
                // 开关打开且服务器未运行时，启动服务器
                startSocketServer()
            } else if (!isChecked && isServerRunning) {
                // 开关关闭且服务器正在运行时，停止服务器
                stopSocketServer()
            }
        }
    }

    /**
     * 启动Socket服务器
     * 在指定端口上创建服务器Socket，等待客户端连接
     */
    private fun startSocketServer() {
        try {
            // 获取端口号，如果无效则使用默认端口
            val port = etPort.text.toString().toIntOrNull() ?: DEFAULT_PORT

            // 验证端口号范围（避免使用系统保留端口）
            if (port < 1024 || port > 65535) {
                throw IllegalArgumentException("端口号必须在1024-65535之间")
            }

            // 在IO线程中启动服务器，并设置异常处理器
            serverJob = coroutineScope.launch(Dispatchers.IO + CoroutineExceptionHandler { _, e ->
                Log.e(TAG, "Server coroutine error: ${e.message}")
                handleServerError(e)
            }) {
                try {
                    // 创建服务器Socket并绑定到指定端口
                    serverSocket = ServerSocket(port)
                    isServerRunning = true
                    Log.d(TAG, "Socket server started on port $port")

                    // 在主线程中显示启动成功消息
                    withContext(Dispatchers.Main) {
                        Toast.makeText(this@MainActivity, "Socket服务器已启动，端口：$port", Toast.LENGTH_SHORT).show()
                    }

                    // 服务器主循环：等待并处理客户端连接
                    while (isActive && isServerRunning) {
                        try {
                            // 等待客户端连接
                            val socket = serverSocket?.accept()
                            if (socket != null) {
                                // 处理客户端连接（在新的协程中）
                                handleClientConnection(socket)
                            }
                        } catch (e: SocketException) {
                            // Socket异常通常表示服务器被关闭
                            if (isServerRunning) {
                                Log.e(TAG, "Socket accept error: ${e.message}")
                            }
                            break
                        }
                    }
                } catch (e: BindException) {
                    // 端口绑定失败（通常是端口被占用）
                    Log.e(TAG, "Port binding error: ${e.message}")
                    handleServerError(e)
                } catch (e: Exception) {
                    // 其他服务器错误
                    Log.e(TAG, "Server error: ${e.message}")
                    handleServerError(e)
                } finally {
                    // 确保服务器Socket被正确关闭
                    closeServerSocket()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error starting socket server: ${e.message}")
            handleServerError(e)
        }
    }

    /**
     * 处理客户端连接
     * 在新的协程中处理每个客户端连接，读取陀螺仪数据并更新UI
     * @param socket 客户端Socket连接
     */
    private fun handleClientConnection(socket: Socket) {
        // 为每个客户端连接创建新的协程，并设置异常处理器
        coroutineScope.launch(Dispatchers.IO + CoroutineExceptionHandler { _, e ->
            Log.e(TAG, "Client connection error: ${e.message}")
            socket.close()  // 发生异常时关闭Socket
        }) {
            try {
                // 创建输入流读取器，用于读取客户端发送的数据
                val reader = BufferedReader(InputStreamReader(socket.getInputStream()))

                // 持续读取客户端数据，直到连接断开或服务器停止
                while (isActive && isServerRunning) {
                    try {
                        // 读取一行数据
                        val line = reader.readLine()

                        if (line != null) {
                            // 解析接收到的数据（期望格式：x,y,z）
                            val parts = line.split(',')
                            if (parts.size == 3) {
                                // 尝试将字符串转换为浮点数
                                val x = parts[0].toFloatOrNull()
                                val y = parts[1].toFloatOrNull()
                                val z = parts[2].toFloatOrNull()

                                Log.d(TAG, "Received data: x=$x, y=$y, z=$z")

                                // 如果所有值都解析成功，更新UI和保存设置
                                if (x != null && y != null && z != null) {
                                    // 切换到主线程更新UI
                                    withContext(Dispatchers.Main) {
                                        etRotationX.setText(x.toString())  // 更新X轴输入框
                                        etRotationY.setText(y.toString())  // 更新Y轴输入框
                                        etRotationZ.setText(z.toString())  // 更新Z轴输入框

                                        // 保存接收到的数据
                                        saveSettings(x, y, z)
                                    }
                                } else {
                                    Log.e(TAG, "Error parsing floats from string: $line")
                                }
                            } else {
                                Log.w(TAG, "Received malformed line (expected 3 parts separated by comma): $line")
                            }
                        } else {
                            // 客户端断开连接或数据流结束
                            Log.d(TAG, "Client disconnected or end of stream.")
                            break
                        }
                    } catch (e: SocketException) {
                        // Socket异常（通常是连接断开）
                        if (isServerRunning) {
                            Log.e(TAG, "Client read error or socket closed: ${e.message}")
                        }
                        break
                    } catch (e: NumberFormatException) {
                        // 数字格式异常（数据格式错误）
                        Log.e(TAG, "Cannot parse float from received string", e)
                    }
                }
            } catch (e: Exception) {
                // 处理其他异常
                Log.e(TAG, "Client connection error: ${e.message}")
            } finally {
                // 确保客户端Socket被正确关闭
                try {
                    socket.close()
                } catch (e: Exception) {
                    Log.e(TAG, "Error closing client socket: ${e.message}")
                }
            }
        }
    }

    /**
     * 处理服务器错误
     * 根据不同的异常类型显示相应的错误消息，并重置服务器状态
     * @param e 发生的异常
     */
    private fun handleServerError(e: Throwable) {
        // 根据异常类型生成用户友好的错误消息
        val errorMessage = when (e) {
            is BindException -> "端口${etPort.text}已被占用，请更换端口"
            is IllegalArgumentException -> e.message ?: "端口号无效"
            else -> "服务器错误：${e.message}"
        }

        // 在主线程中显示错误消息并重置UI状态
        coroutineScope.launch(Dispatchers.Main) {
            Toast.makeText(this@MainActivity, errorMessage, Toast.LENGTH_LONG).show()
            switchSocket.isChecked = false  // 关闭开关
            isServerRunning = false         // 重置服务器运行状态
        }
    }

    /**
     * 关闭服务器Socket
     * 安全地关闭服务器Socket并重置相关状态
     */
    private fun closeServerSocket() {
        try {
            serverSocket?.close()    // 关闭服务器Socket
            serverSocket = null      // 清空引用
            isServerRunning = false  // 重置运行状态
        } catch (e: Exception) {
            Log.e(TAG, "Error closing server socket: ${e.message}")
        }
    }

    /**
     * 停止Socket服务器
     * 取消服务器协程，关闭Socket，并显示停止消息
     */
    private fun stopSocketServer() {
        try {
            isServerRunning = false  // 设置停止标志
            serverJob?.cancel()      // 取消服务器协程
            closeServerSocket()      // 关闭服务器Socket
            Log.d(TAG, "Socket server stopped")
            Toast.makeText(this, "Socket服务器已停止", Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping socket server: ${e.message}")
        }
    }

    /**
     * Activity销毁时的回调方法
     * 清理资源：停止Socket服务器并取消所有协程
     */
    override fun onDestroy() {
        super.onDestroy()
        stopSocketServer()      // 停止Socket服务器
        coroutineScope.cancel() // 取消所有协程，防止内存泄漏
    }
} 