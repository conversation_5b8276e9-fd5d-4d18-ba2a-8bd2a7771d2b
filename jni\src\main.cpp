#include <iostream>
#include <string>
#include <vector>
#include <thread>
#include <chrono>
#include <stdexcept>
#include <cmath>
#include "GyroHook.hpp"

const std::string DEFAULT_IP = "127.0.0.1";
const int DEFAULT_PORT = 16384;
const std::string DEFAULT_FILE_PATH = "/data/user/0/com.example.gyrohook/shared_prefs/gyro_settings.xml";

void printUsage(const char* progName) {
    std::cerr << "用法: " << progName << " [ip_address] [port]\n"
              << "自动启动Socket模式的持续陀螺仪数据发送\n"
              << "参数:\n"
              << "  ip_address      服务器IP地址 (默认: " << DEFAULT_IP << ")\n"
              << "  port            服务器端口 (默认: " << DEFAULT_PORT << ")\n"
              << "示例:\n"
              << "  " << progName << "                    (连接到 127.0.0.1:16384)\n"
              << "  " << progName << " *************      (连接到 *************:16384)\n"
              << "  " << progName << " ************* 8080 (连接到 *************:8080)\n"
              << std::endl;
}

void simulateGyroMovement(GyroClient& client) {
    float x = 0.0f, y = 0.0f, z = 0.0f;
    const float step = 0.1f;
    int iteration = 0;

    std::cout << "Socket模式: 开始模拟持续发送陀螺仪数据... (按Ctrl+C停止)" << std::endl;
    while (client.isConnected()) {
        x += step;
        if (x > 5.0f) x = -5.0f;

        y = 2.0f * std::sin(static_cast<double>(x));

        z = 1.5f * std::cos(static_cast<double>(x));

        std::cout << "发送: X=" << x << ", Y=" << y << ", Z=" << z << std::endl;
        if (!client.sendGyroData(x, y, z)) {
            std::cerr << "发送数据失败，断开连接。" << std::endl;
            client.disconnect();
            break;
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        iteration++;
    }
}

int main(int argc, char* argv[]) {
    // 默认启动socket模式的simulateGyroMovement
    std::string ip = DEFAULT_IP;
    int port = DEFAULT_PORT;

    // 可选参数：IP和端口
    if (argc > 1) ip = argv[1];
    if (argc > 2) port = std::stoi(argv[2]);

    try {
        std::cout << "Socket模式: 尝试连接到 " << ip << ":" << port << std::endl;
        GyroClient client(ip, port);
        if (!client.connect()) {
            std::cerr << "无法连接到服务器。" << std::endl;
            return 1;
        }

        // 直接启动持续发送模式
        simulateGyroMovement(client);

    } catch (const std::exception& e) {
        std::cerr << "发生错误: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}