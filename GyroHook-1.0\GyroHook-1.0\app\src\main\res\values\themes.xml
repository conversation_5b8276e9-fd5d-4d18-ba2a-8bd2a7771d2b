<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.GyroHook" parent="Theme.MaterialComponents.NoActionBar">
        <!-- 主要颜色 -->
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryDark">@color/primary_dark</item>
        <item name="colorAccent">@color/accent_color</item>
        
        <!-- 背景颜色 -->
        <item name="android:windowBackground">@color/background_color</item>
        
        <!-- 状态栏和导航栏设置 -->
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        
        <!-- 文字颜色 -->
        <item name="android:textColor">@color/text_color</item>
        <item name="android:textColorHint">@color/hint_color</item>
        
        <!-- 输入框样式 -->
        <item name="textInputStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox</item>
    </style>
</resources>