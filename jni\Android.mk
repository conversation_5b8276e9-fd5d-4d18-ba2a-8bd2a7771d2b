LOCAL_PATH := $(call my-dir)

include $(CLEAR_VARS)

LOCAL_MODULE := bs.sh
LOCAL_CFLAGS := -std=c++17
LOCAL_CPPFLAGS := -std=c++17
LOCAL_CFLAGS += -Wno-error=format-security -w
LOCAL_CFLAGS += -fno-rtti -fno-exceptions -fpermissive -fvisibility=hidden
LOCAL_CPPFLAGS += -Wno-error=format-security -fpermissive -w -Werror -s 
LOCAL_CPPFLAGS += -fno-rtti -fno-exceptions -fms-extensions -Wno-error=c++11-narrowing -fvisibility=hidden

# 基础混淆配置
LOCAL_CFLAGS += -O2 -ffunction-sections -fdata-sections
LOCAL_CFLAGS += -fvisibility=hidden -fvisibility-inlines-hidden
LOCAL_CFLAGS += -fomit-frame-pointer -funroll-loops -fno-stack-protector
LOCAL_CPPFLAGS += -O2 -ffunction-sections -fdata-sections
LOCAL_CPPFLAGS += -fvisibility=hidden -fvisibility-inlines-hidden
LOCAL_CPPFLAGS += -fomit-frame-pointer -funroll-loops -fno-stack-protector

# 添加头文件搜索路径
LOCAL_C_INCLUDES := $(LOCAL_PATH)/include

# 添加源文件
LOCAL_SRC_FILES := src/main.cpp \


# 链接库和系统库（移除-lz因为我们使用静态库）
LOCAL_LDLIBS += -llog -landroid -lEGL -lGLESv1_CM -lGLESv2 -lGLESv3

# 构建可执行文件
include $(BUILD_EXECUTABLE)

